<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <Nullable>enable</Nullable>
    <EnableDefaultApplicationDefinition>false</EnableDefaultApplicationDefinition>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="spec-kit\src\specify_cli\__init__.py" />
    <Content Include="spec-kit\LICENSE" />
    <Content Include="spec-kit\README.md" />
    <Content Include="spec-kit\SECURITY.md" />
    <Content Include="QuasarDysonAnimation.cs" />
    <Content Include="MainWindow.xaml.cs" />
    <Content Include="MainWindow.xaml" />
  </ItemGroup>
</Project>