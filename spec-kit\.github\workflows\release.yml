name: Create Release

on:
  push:
    branches: [ main ]
    paths:
      - 'memory/**'
      - 'scripts/**'
      - 'templates/**'
      - '.github/workflows/**'
  workflow_dispatch:

jobs:
  release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}
      - name: Get latest tag
        id: get_tag
        run: |
          # Get the latest tag, or use v0.0.0 if no tags exist
          LATEST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
          echo "latest_tag=$LATEST_TAG" >> $GITHUB_OUTPUT
          
          # Extract version number and increment
          VERSION=$(echo $LATEST_TAG | sed 's/v//')
          IFS='.' read -ra VERSION_PARTS <<< "$VERSION"
          MAJOR=${VERSION_PARTS[0]:-0}
          MINOR=${VERSION_PARTS[1]:-0}
          PATCH=${VERSION_PARTS[2]:-0}
          
          # Increment patch version
          PATCH=$((PATCH + 1))
          NEW_VERSION="v$MAJOR.$MINOR.$PATCH"
          
          echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "New version will be: $NEW_VERSION"
      - name: Check if release already exists
        id: check_release
        run: |
          if gh release view ${{ steps.get_tag.outputs.new_version }} >/dev/null 2>&1; then
            echo "exists=true" >> $GITHUB_OUTPUT
            echo "Release ${{ steps.get_tag.outputs.new_version }} already exists, skipping..."
          else
            echo "exists=false" >> $GITHUB_OUTPUT
            echo "Release ${{ steps.get_tag.outputs.new_version }} does not exist, proceeding..."
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Create release package variants
        if: steps.check_release.outputs.exists == 'false'
        run: |
          chmod +x .github/workflows/scripts/create-release-packages.sh
          .github/workflows/scripts/create-release-packages.sh ${{ steps.get_tag.outputs.new_version }}
      - name: Generate release notes
        if: steps.check_release.outputs.exists == 'false'
        id: release_notes
        run: |
          # Get commits since last tag
          LAST_TAG=${{ steps.get_tag.outputs.latest_tag }}
          if [ "$LAST_TAG" = "v0.0.0" ]; then
            # Check how many commits we have and use that as the limit
            COMMIT_COUNT=$(git rev-list --count HEAD)
            if [ "$COMMIT_COUNT" -gt 10 ]; then
              COMMITS=$(git log --oneline --pretty=format:"- %s" HEAD~10..HEAD)
            else
              COMMITS=$(git log --oneline --pretty=format:"- %s" HEAD~$COMMIT_COUNT..HEAD 2>/dev/null || git log --oneline --pretty=format:"- %s")
            fi
          else
            COMMITS=$(git log --oneline --pretty=format:"- %s" $LAST_TAG..HEAD)
          fi
          
          # Create release notes
          cat > release_notes.md << EOF
          Template release ${{ steps.get_tag.outputs.new_version }}

          Updated specification-driven development templates for GitHub Copilot, Claude Code, Gemini CLI, and Cursor.

          Now includes per-script variants for POSIX shell (sh) and PowerShell (ps).

          Download the template for your preferred AI assistant + script type:
          - spec-kit-template-copilot-sh-${{ steps.get_tag.outputs.new_version }}.zip
          - spec-kit-template-copilot-ps-${{ steps.get_tag.outputs.new_version }}.zip
          - spec-kit-template-claude-sh-${{ steps.get_tag.outputs.new_version }}.zip
          - spec-kit-template-claude-ps-${{ steps.get_tag.outputs.new_version }}.zip
          - spec-kit-template-gemini-sh-${{ steps.get_tag.outputs.new_version }}.zip
          - spec-kit-template-gemini-ps-${{ steps.get_tag.outputs.new_version }}.zip
          - spec-kit-template-cursor-sh-${{ steps.get_tag.outputs.new_version }}.zip
          - spec-kit-template-cursor-ps-${{ steps.get_tag.outputs.new_version }}.zip
          EOF
          
          echo "Generated release notes:"
          cat release_notes.md
      - name: Create GitHub Release
        if: steps.check_release.outputs.exists == 'false'
        run: |
          # Remove 'v' prefix from version for release title
          VERSION_NO_V=${{ steps.get_tag.outputs.new_version }}
          VERSION_NO_V=${VERSION_NO_V#v}
          
          gh release create ${{ steps.get_tag.outputs.new_version }} \
            spec-kit-template-copilot-sh-${{ steps.get_tag.outputs.new_version }}.zip \
            spec-kit-template-copilot-ps-${{ steps.get_tag.outputs.new_version }}.zip \
            spec-kit-template-claude-sh-${{ steps.get_tag.outputs.new_version }}.zip \
            spec-kit-template-claude-ps-${{ steps.get_tag.outputs.new_version }}.zip \
            spec-kit-template-gemini-sh-${{ steps.get_tag.outputs.new_version }}.zip \
            spec-kit-template-gemini-ps-${{ steps.get_tag.outputs.new_version }}.zip \
            spec-kit-template-cursor-sh-${{ steps.get_tag.outputs.new_version }}.zip \
            spec-kit-template-cursor-ps-${{ steps.get_tag.outputs.new_version }}.zip \
            --title "Spec Kit Templates - $VERSION_NO_V" \
            --notes-file release_notes.md
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Update version in pyproject.toml (for release artifacts only)
        if: steps.check_release.outputs.exists == 'false'
        run: |
          # Update version in pyproject.toml (remove 'v' prefix for Python versioning)
          VERSION=${{ steps.get_tag.outputs.new_version }}
          PYTHON_VERSION=${VERSION#v}
          
          if [ -f "pyproject.toml" ]; then
            sed -i "s/version = \".*\"/version = \"$PYTHON_VERSION\"/" pyproject.toml
            echo "Updated pyproject.toml version to $PYTHON_VERSION (for release artifacts only)"
          fi
