D:\Photonic-Quantum\bin\Debug\net6.0-windows\PhotonicQuantumAnimation.exe
D:\Photonic-Quantum\bin\Debug\net6.0-windows\PhotonicQuantumAnimation.deps.json
D:\Photonic-Quantum\bin\Debug\net6.0-windows\PhotonicQuantumAnimation.runtimeconfig.json
D:\Photonic-Quantum\bin\Debug\net6.0-windows\PhotonicQuantumAnimation.dll
D:\Photonic-Quantum\bin\Debug\net6.0-windows\PhotonicQuantumAnimation.pdb
D:\Photonic-Quantum\obj\Debug\net6.0-windows\MainWindow.baml
D:\Photonic-Quantum\obj\Debug\net6.0-windows\MainWindow.g.cs
D:\Photonic-Quantum\obj\Debug\net6.0-windows\App.g.cs
D:\Photonic-Quantum\obj\Debug\net6.0-windows\PhotonicQuantumAnimation_MarkupCompile.cache
D:\Photonic-Quantum\obj\Debug\net6.0-windows\PhotonicQuantumAnimation.g.resources
D:\Photonic-Quantum\obj\Debug\net6.0-windows\PhotonicQuantumAnimation.GeneratedMSBuildEditorConfig.editorconfig
D:\Photonic-Quantum\obj\Debug\net6.0-windows\PhotonicQuantumAnimation.AssemblyInfoInputs.cache
D:\Photonic-Quantum\obj\Debug\net6.0-windows\PhotonicQuantumAnimation.AssemblyInfo.cs
D:\Photonic-Quantum\obj\Debug\net6.0-windows\PhotonicQuantumAnimation.csproj.CoreCompileInputs.cache
D:\Photonic-Quantum\obj\Debug\net6.0-windows\PhotonicQuantumAnimation.sourcelink.json
D:\Photonic-Quantum\obj\Debug\net6.0-windows\PhotonicQuantumAnimation.dll
D:\Photonic-Quantum\obj\Debug\net6.0-windows\refint\PhotonicQuantumAnimation.dll
D:\Photonic-Quantum\obj\Debug\net6.0-windows\PhotonicQuantumAnimation.pdb
D:\Photonic-Quantum\obj\Debug\net6.0-windows\PhotonicQuantumAnimation.genruntimeconfig.cache
D:\Photonic-Quantum\obj\Debug\net6.0-windows\ref\PhotonicQuantumAnimation.dll
D:\Photonic-Quantum\obj\Debug\net6.0-windows\PhotonicQuantumAnimation_Content.g.cs
