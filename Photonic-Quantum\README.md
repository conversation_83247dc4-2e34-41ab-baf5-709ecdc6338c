# PQA (Photonic Quantum Animation)

PQA integrates the [spec-kit](https://github.com/xingxerx/spec-kit) toolkit into this workspace, providing advanced specification-driven development tools for your project.

## Features
- **/specify**: Define and refine requirements and specifications.
- **/plan**: Generate actionable plans from specifications.
- **/task**: Manage and track tasks derived from plans.
- **/constitution**: Set and enforce project rules and guidelines.
- **/implement**: Guide and automate implementation steps.

## Getting Started
1. Ensure you have Python 3.8+ installed.
2. Install dependencies:
   ```sh
   pip install -e ./spec-kit
   ```
3. Use the CLI commands from the `spec-kit` package:
   ```sh
   specify /specify
   specify /plan
   specify /task
   specify /constitution
   specify /implement
   ```

## Project Structure
- `spec-kit/` — Contains the spec-kit source code and CLI.
- `PhotonicQuantumAnimation.csproj` — Main C# project for quantum animation.
- `README.md` — This file.

## License
See `spec-kit/LICENSE` for licensing details.

---
For more information, visit the [spec-kit GitHub repository](https://github.com/xingxerx/spec-kit).
