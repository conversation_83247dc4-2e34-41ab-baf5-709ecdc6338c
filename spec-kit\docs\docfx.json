{"build": {"content": [{"files": ["*.md", "toc.yml"]}, {"files": ["../README.md", "../CONTRIBUTING.md", "../CODE_OF_CONDUCT.md", "../SECURITY.md", "../SUPPORT.md"], "dest": "."}], "resource": [{"files": ["images/**"]}, {"files": ["../media/**"], "dest": "media"}], "overwrite": [{"files": ["apidoc/**.md"], "exclude": ["obj/**", "_site/**"]}], "dest": "_site", "globalMetadataFiles": [], "fileMetadataFiles": [], "template": ["default", "modern"], "postProcessors": [], "markdownEngineName": "markdig", "noLangKeyword": false, "keepFileLink": false, "cleanupCacheHistory": false, "disableGitFeatures": false, "globalMetadata": {"_appTitle": "Spec Kit Documentation", "_appName": "Spec Kit", "_appFooter": "Spec Kit - A specification-driven development toolkit", "_enableSearch": true, "_disableContribution": false, "_gitContribute": {"repo": "https://github.com/github/spec-kit", "branch": "main"}}}}